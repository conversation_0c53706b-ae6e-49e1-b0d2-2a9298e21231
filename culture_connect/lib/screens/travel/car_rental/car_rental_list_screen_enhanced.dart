import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/car_rental_filter_provider.dart';
import 'package:culture_connect/screens/travel/car_rental/car_rental_details_screen.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/travel/car_rental_filter_dialog.dart';
import 'package:culture_connect/widgets/travel/car_rental_grid_item.dart';
import 'package:culture_connect/widgets/travel/car_rental_skeleton_loading.dart';
import 'package:culture_connect/widgets/travel/car_rental_sort_selector.dart';
import 'package:culture_connect/widgets/travel/travel_service_card.dart';

/// Enhanced screen for displaying a list of car rentals
class CarRentalListScreenEnhanced extends ConsumerStatefulWidget {
  /// Creates a new car rental list screen
  const CarRentalListScreenEnhanced({super.key});

  @override
  ConsumerState<CarRentalListScreenEnhanced> createState() =>
      _CarRentalListScreenEnhancedState();
}

class _CarRentalListScreenEnhancedState
    extends ConsumerState<CarRentalListScreenEnhanced>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  bool _isGridView = true;
  late AnimationController _animationController;
  late Animation<double> _animation;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();

    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => const CarRentalFilterDialog(),
    );
  }

  void _toggleViewMode() {
    setState(() {
      _isGridView = !_isGridView;
      _animationController.reset();
      _animationController.forward();
    });
  }

  void _navigateToCarRentalDetails(CarRental carRental) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CarRentalDetailsScreen(carRental: carRental),
      ),
    );
  }

  List<CarRental> _filterBySearch(List<CarRental> carRentals) {
    if (_searchQuery.isEmpty) return carRentals;

    final query = _searchQuery.toLowerCase();
    return carRentals.where((car) {
      return car.name.toLowerCase().contains(query) ||
          car.make.toLowerCase().contains(query) ||
          car.model.toLowerCase().contains(query) ||
          car.rentalCompany.toLowerCase().contains(query) ||
          car.location.toLowerCase().contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search car rentals...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                      color: theme.colorScheme.onSurface.withAlpha(153)),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
                autofocus: true,
              )
            : const Text('Car Rentals'),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            tooltip: _isSearching ? 'Clear search' : 'Search',
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
          const CarRentalSortSelector(),
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            tooltip: _isGridView ? 'List view' : 'Grid view',
            onPressed: _toggleViewMode,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter',
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: _buildCarRentalList(),
    );
  }

  Widget _buildCarRentalList() {
    final filteredCarRentalsAsyncValue = ref.watch(filteredCarRentalsProvider);

    return filteredCarRentalsAsyncValue.when(
      data: (carRentals) {
        final searchFilteredCarRentals = _filterBySearch(carRentals);

        if (searchFilteredCarRentals.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.car_rental,
                  size: 64,
                  color: Theme.of(context)
                      .colorScheme
                      .onSurfaceVariant
                      .withAlpha(128),
                ),
                const SizedBox(height: 16),
                Text(
                  _searchQuery.isNotEmpty
                      ? 'No car rentals found for "$_searchQuery"'
                      : 'No car rentals available',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                if (_searchQuery.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _searchController.clear();
                      });
                    },
                    child: const Text('Clear Search'),
                  ),
                ],
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            // Refresh data
            ref.invalidate(filteredCarRentalsProvider);
          },
          child: FadeTransition(
            opacity: _animation,
            child: _isGridView
                ? _buildGridView(searchFilteredCarRentals)
                : _buildListView(searchFilteredCarRentals),
          ),
        );
      },
      loading: () => CarRentalSkeletonLoading(isGrid: _isGridView),
      error: (error, stackTrace) => Center(
        child: ErrorView(
          error: error.toString(),
          onRetry: () => ref.invalidate(filteredCarRentalsProvider),
        ),
      ),
    );
  }

  Widget _buildGridView(List<CarRental> carRentals) {
    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: carRentals.length,
      itemBuilder: (context, index) {
        final carRental = carRentals[index];
        return CarRentalGridItem(
          carRental: carRental,
          onTap: () => _navigateToCarRentalDetails(carRental),
        );
      },
    );
  }

  Widget _buildListView(List<CarRental> carRentals) {
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(16),
      itemCount: carRentals.length,
      itemBuilder: (context, index) {
        final carRental = carRentals[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 16),
          child: TravelServiceCard(
            travelService: carRental,
            onTap: () => _navigateToCarRentalDetails(carRental),
          ),
        );
      },
    );
  }
}
